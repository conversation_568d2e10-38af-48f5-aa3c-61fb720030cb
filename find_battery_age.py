this will use working_vehicles.csv, working_unique_vehicles.csv, hv_repair_2025-06-02b.csv

Goal is to find the age of each battery that appear in those files. Use data_preparation_pipeline.py for inspiration
Because to calculate age, we only care about start day of battery. Then use today's date to calculate the age. 

Steps:
1. Read hv_repair, use effective date (date of battery_changed, fallback to created if not present). We only care about the first appearance or start day of battery in this file. Example effective date is day 15, battery changed from old bat bat1 to new bat bat2. Then we will take note that bat1 and bat2 first appearance is on day15.  For each battery, I think we have to take note start date, and all of its associated vehicles. No matter new or old bat, if an event of battery change happen for a vehicle, then the batteries of that event will assosiate with that vehicle
2. We will deal with completeness of vehicles. For all unique vehicles that appear in  working_vehicles.csv, working_unique_vehicles.csv, hv_repair_2025-06-02b.csv. They must have a battery. 
    3a. For vehicles that only appear in hv_repair. Then we can assume that erszulassung date of that vehicle is the effective date of the first battery change. Please look at note 1 
    3b. For vehicles that appear in working_vehicles.csv, working_unique_vehicles.csv and also in hv_repair. Then use ersztulassung date of that vehicle. Please look at note 1 
    3c. For vehicles that appear in working_vehicles.csv, working_unique_vehicles.csv and not in hv_repair. Then simply assign start date of batteries to ersztulassung date of that vehicle. Batteries are the columns master and slave in those 2 files. Even though the vehicles is not in hv_repair, the batteries may still appear in hv_repair. 


Note: 
1.Because a vehicle from erstzulassung to the first event it has in hv_repair, it must ride with a battery. This battery is the old bat that appear in hv_repair (not the new bat). Example: bat1 first appear in hv_repair on day 15, vehicle1 is associated with bat1 on day 15. THen we find out that vehicle1 ertszulassung is on day 10. Then we will make changes to bat1, it will have a new start day of 10. There might be a case that an earliest event of that vehicle only have old bat or new bat column (techniker may have forgot to fill in the other column). In this case, just use that only 1 battery
2. Whenever we assign the start date to a battery, always check if it already has a start date. If it is, then we will use the earlier start date